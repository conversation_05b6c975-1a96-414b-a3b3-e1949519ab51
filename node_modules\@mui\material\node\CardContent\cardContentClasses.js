"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
exports.getCardContentUtilityClass = getCardContentUtilityClass;
var _generateUtilityClasses = _interopRequireDefault(require("@mui/utils/generateUtilityClasses"));
var _generateUtilityClass = _interopRequireDefault(require("@mui/utils/generateUtilityClass"));
function getCardContentUtilityClass(slot) {
  return (0, _generateUtilityClass.default)('MuiCardContent', slot);
}
const cardContentClasses = (0, _generateUtilityClasses.default)('MuiCardContent', ['root']);
var _default = exports.default = cardContentClasses;