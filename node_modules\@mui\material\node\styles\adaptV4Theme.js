"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = adaptV4Theme;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _system = require("@mui/system");
const _excluded = ["defaultProps", "mixins", "overrides", "palette", "props", "styleOverrides"],
  _excluded2 = ["type", "mode"];
function adaptV4Theme(inputTheme) {
  if (process.env.NODE_ENV !== 'production') {
    console.warn(['MUI: adaptV4Theme() is deprecated.', 'Follow the upgrade guide on https://mui.com/r/migration-v4#theme.'].join('\n'));
  }
  const {
      defaultProps = {},
      mixins = {},
      overrides = {},
      palette = {},
      props = {},
      styleOverrides = {}
    } = inputTheme,
    other = (0, _objectWithoutPropertiesLoose2.default)(inputTheme, _excluded);
  const theme = (0, _extends2.default)({}, other, {
    components: {}
  });

  // default props
  Object.keys(defaultProps).forEach(component => {
    const componentValue = theme.components[component] || {};
    componentValue.defaultProps = defaultProps[component];
    theme.components[component] = componentValue;
  });
  Object.keys(props).forEach(component => {
    const componentValue = theme.components[component] || {};
    componentValue.defaultProps = props[component];
    theme.components[component] = componentValue;
  });

  // CSS overrides
  Object.keys(styleOverrides).forEach(component => {
    const componentValue = theme.components[component] || {};
    componentValue.styleOverrides = styleOverrides[component];
    theme.components[component] = componentValue;
  });
  Object.keys(overrides).forEach(component => {
    const componentValue = theme.components[component] || {};
    componentValue.styleOverrides = overrides[component];
    theme.components[component] = componentValue;
  });

  // theme.spacing
  theme.spacing = (0, _system.createSpacing)(inputTheme.spacing);

  // theme.mixins.gutters
  const breakpoints = (0, _system.createBreakpoints)(inputTheme.breakpoints || {});
  const spacing = theme.spacing;
  theme.mixins = (0, _extends2.default)({
    gutters: (styles = {}) => {
      return (0, _extends2.default)({
        paddingLeft: spacing(2),
        paddingRight: spacing(2)
      }, styles, {
        [breakpoints.up('sm')]: (0, _extends2.default)({
          paddingLeft: spacing(3),
          paddingRight: spacing(3)
        }, styles[breakpoints.up('sm')])
      });
    }
  }, mixins);
  const {
      type: typeInput,
      mode: modeInput
    } = palette,
    paletteRest = (0, _objectWithoutPropertiesLoose2.default)(palette, _excluded2);
  const finalMode = modeInput || typeInput || 'light';
  theme.palette = (0, _extends2.default)({
    // theme.palette.text.hint
    text: {
      hint: finalMode === 'dark' ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.38)'
    },
    mode: finalMode,
    type: finalMode
  }, paletteRest);
  return theme;
}