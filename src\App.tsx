import React, { useState } from 'react';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';
import theme from './theme';
import Header from './components/layout/Header';
import HeroSection from './components/sections/HeroSection';
import FeaturedCollections from './components/sections/FeaturedCollections';
import ProductGallery from './components/sections/ProductGallery';
import ProductDetail from './components/sections/ProductDetail';
import AboutSection from './components/sections/AboutSection';
import ContactSection from './components/sections/ContactSection';
import { Product, ProductCategory, Language } from './types/product';

const createEmotionCache = () => {
  return createCache({
    key: "mui",
    prepend: true,
  });
};

const emotionCache = createEmotionCache();

const App: React.FC = () => {
  const [currentLanguage, setCurrentLanguage] = useState<Language>({
    code: 'en',
    name: 'English',
    direction: 'ltr'
  });
  const [selectedCategory, setSelectedCategory] = useState<ProductCategory | undefined>();
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [productDetailOpen, setProductDetailOpen] = useState(false);

  const handleLanguageChange = (language: Language) => {
    setCurrentLanguage(language);
    // In a real app, this would trigger translation updates
    document.dir = language.direction;
  };

  const handleCategorySelect = (category: ProductCategory) => {
    setSelectedCategory(category);
    // Scroll to gallery section
    const galleryElement = document.getElementById('product-gallery');
    if (galleryElement) {
      galleryElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleProductSelect = (product: Product) => {
    setSelectedProduct(product);
    setProductDetailOpen(true);
  };

  const handleProductDetailClose = () => {
    setProductDetailOpen(false);
    setSelectedProduct(null);
  };

  return (
    <CacheProvider value={emotionCache}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <div className="min-h-screen bg-white">
          <Header 
            currentLanguage={currentLanguage}
            onLanguageChange={handleLanguageChange}
          />
          
          <main>
            <HeroSection />
            
            <FeaturedCollections 
              onCategorySelect={handleCategorySelect}
            />
            
            <div id="product-gallery">
              <ProductGallery 
                selectedCategory={selectedCategory}
                onProductSelect={handleProductSelect}
              />
            </div>
            
            <AboutSection />
            
            <ContactSection />
          </main>

          <ProductDetail
            product={selectedProduct}
            open={productDetailOpen}
            onClose={handleProductDetailClose}
          />
        </div>
      </ThemeProvider>
    </CacheProvider>
  );
};

export default App;