import React, { useState } from 'react';
import { A<PERSON><PERSON><PERSON>, Too<PERSON><PERSON>, Typography, IconButton, <PERSON>ack, useMediaQuery, useTheme } from '@mui/material';
import { Menu, Globe, X } from 'lucide-react';
import { Language } from '../../types/product';

interface HeaderProps {
  currentLanguage: Language;
  onLanguageChange: (language: Language) => void;
}

const languages: Language[] = [
  { code: 'en', name: 'English', direction: 'ltr' },
  { code: 'ar', name: 'العربية', direction: 'rtl' }
];

const Header: React.FC<HeaderProps> = ({ currentLanguage, onLanguageChange }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleLanguageToggle = () => {
    const nextLanguage = languages.find(lang => lang.code !== currentLanguage.code) || languages[0];
    onLanguageChange(nextLanguage);
  };

  return (
    <AppBar 
      position="fixed" 
      className="bg-white/80 backdrop-blur-lg border-b border-gray-200/50"
      elevation={0}
      sx={{ 
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        backdropFilter: 'blur(20px)',
        borderBottom: '1px solid rgba(222, 216, 211, 0.5)'
      }}
    >
      <Toolbar className="px-4 md:px-8">
        <Typography
          variant="h5"
          component="div"
          className="flex-1 font-bold"
          sx={{ 
            fontFamily: '"Playfair Display", serif',
            color: 'primary.main',
            fontWeight: 700
          }}
        >
          Luxe Stone
        </Typography>

        {!isMobile && (
          <Stack direction="row" spacing={4} className="mr-8">
            <Typography 
              variant="body1" 
              className="cursor-pointer hover:text-yellow-600 transition-colors"
              sx={{ fontWeight: 500 }}
            >
              Home
            </Typography>
            <Typography 
              variant="body1" 
              className="cursor-pointer hover:text-yellow-600 transition-colors"
              sx={{ fontWeight: 500 }}
            >
              Gallery
            </Typography>
            <Typography 
              variant="body1" 
              className="cursor-pointer hover:text-yellow-600 transition-colors"
              sx={{ fontWeight: 500 }}
            >
              About
            </Typography>
            <Typography 
              variant="body1" 
              className="cursor-pointer hover:text-yellow-600 transition-colors"
              sx={{ fontWeight: 500 }}
            >
              Contact
            </Typography>
          </Stack>
        )}

        <IconButton
          onClick={handleLanguageToggle}
          className="mr-2 hover:bg-yellow-50 transition-colors"
          sx={{ color: 'primary.main' }}
        >
          <Globe size={24} />
        </IconButton>

        {isMobile && (
          <IconButton
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            sx={{ color: 'primary.main' }}
          >
            {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </IconButton>
        )}
      </Toolbar>

      {/* Mobile Menu */}
      {isMobile && mobileMenuOpen && (
        <div className="bg-white/95 backdrop-blur-lg border-t border-gray-200/50 px-4 py-6">
          <Stack spacing={3}>
            <Typography variant="body1" className="cursor-pointer hover:text-yellow-600 transition-colors">
              Home
            </Typography>
            <Typography variant="body1" className="cursor-pointer hover:text-yellow-600 transition-colors">
              Gallery
            </Typography>
            <Typography variant="body1" className="cursor-pointer hover:text-yellow-600 transition-colors">
              About
            </Typography>
            <Typography variant="body1" className="cursor-pointer hover:text-yellow-600 transition-colors">
              Contact
            </Typography>
          </Stack>
        </div>
      )}
    </AppBar>
  );
};

export default Header;