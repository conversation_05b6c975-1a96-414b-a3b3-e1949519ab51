import React from 'react';
import { Container, Typography, Stack, Box, TextField, Grid } from '@mui/material';
import { Mail, Phone, MapPin, Clock } from 'lucide-react';
import { LuxuryButton, GlassmorphicCard } from '../styled/StyledComponents';

const ContactSection: React.FC = () => {
  return (
    <Box className="py-20 bg-gradient-to-b from-gray-50 to-white">
      <Container maxWidth="lg">
        <Stack spacing={12}>
          {/* Section Header */}
          <Box className="text-center">
            <Typography
              variant="h2"
              className="mb-4"
              sx={{
                fontFamily: '"Playfair Display", serif',
                fontWeight: 600,
                background: 'linear-gradient(135deg, #333333 0%, #eac147 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
              }}
            >
              Get in Touch
            </Typography>
            <Typography
              variant="h6"
              className="text-gray-600 max-w-2xl mx-auto"
              sx={{ fontFamily: '"Inter", sans-serif', fontWeight: 300 }}
            >
              Ready to transform your space with premium stones and ceramics? 
              Our experts are here to help you find the perfect materials for your project.
            </Typography>
          </Box>

          <Grid container spacing={8}>
            {/* Contact Form */}
            <Grid item xs={12} lg={7}>
              <GlassmorphicCard className="p-8">
                <Typography
                  variant="h4"
                  className="mb-6"
                  sx={{
                    fontFamily: '"Playfair Display", serif',
                    fontWeight: 600,
                    color: 'primary.main'
                  }}
                >
                  Send us a Message
                </Typography>

                <Stack spacing={4}>
                  <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3}>
                    <TextField
                      fullWidth
                      label="First Name"
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '12px',
                        }
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Last Name"
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '12px',
                        }
                      }}
                    />
                  </Stack>

                  <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3}>
                    <TextField
                      fullWidth
                      label="Email Address"
                      type="email"
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '12px',
                        }
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Phone Number"
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '12px',
                        }
                      }}
                    />
                  </Stack>

                  <TextField
                    fullWidth
                    label="Project Type"
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '12px',
                      }
                    }}
                  />

                  <TextField
                    fullWidth
                    label="Message"
                    multiline
                    rows={6}
                    variant="outlined"
                    placeholder="Tell us about your project, preferred materials, timeline, and any specific requirements..."
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '12px',
                      }
                    }}
                  />

                  <LuxuryButton size="large" fullWidth>
                    Send Message
                  </LuxuryButton>
                </Stack>
              </GlassmorphicCard>
            </Grid>

            {/* Contact Information */}
            <Grid item xs={12} lg={5}>
              <Stack spacing={6}>
                {/* Contact Details */}
                <Box>
                  <Typography
                    variant="h4"
                    className="mb-6"
                    sx={{
                      fontFamily: '"Playfair Display", serif',
                      fontWeight: 600,
                      color: 'primary.main'
                    }}
                  >
                    Contact Information
                  </Typography>

                  <Stack spacing={4}>
                    <Stack direction="row" spacing={3} alignItems="center">
                      <Box
                        className="w-12 h-12 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 
                                 flex items-center justify-center"
                      >
                        <Mail size={20} className="text-white" />
                      </Box>
                      <Box>
                        <Typography variant="h6" className="font-semibold mb-1">
                          Email Us
                        </Typography>
                        <Typography variant="body2" className="text-gray-600">
                          <EMAIL>
                        </Typography>
                        <Typography variant="body2" className="text-gray-600">
                          <EMAIL>
                        </Typography>
                      </Box>
                    </Stack>

                    <Stack direction="row" spacing={3} alignItems="center">
                      <Box
                        className="w-12 h-12 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 
                                 flex items-center justify-center"
                      >
                        <Phone size={20} className="text-white" />
                      </Box>
                      <Box>
                        <Typography variant="h6" className="font-semibold mb-1">
                          Call Us
                        </Typography>
                        <Typography variant="body2" className="text-gray-600">
                          +****************
                        </Typography>
                        <Typography variant="body2" className="text-gray-600">
                          +****************
                        </Typography>
                      </Box>
                    </Stack>

                    <Stack direction="row" spacing={3} alignItems="center">
                      <Box
                        className="w-12 h-12 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 
                                 flex items-center justify-center"
                      >
                        <MapPin size={20} className="text-white" />
                      </Box>
                      <Box>
                        <Typography variant="h6" className="font-semibold mb-1">
                          Visit Our Showroom
                        </Typography>
                        <Typography variant="body2" className="text-gray-600">
                          123 Luxury Stone Avenue
                        </Typography>
                        <Typography variant="body2" className="text-gray-600">
                          Design District, NY 10001
                        </Typography>
                      </Box>
                    </Stack>

                    <Stack direction="row" spacing={3} alignItems="center">
                      <Box
                        className="w-12 h-12 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 
                                 flex items-center justify-center"
                      >
                        <Clock size={20} className="text-white" />
                      </Box>
                      <Box>
                        <Typography variant="h6" className="font-semibold mb-1">
                          Business Hours
                        </Typography>
                        <Typography variant="body2" className="text-gray-600">
                          Mon - Fri: 9:00 AM - 6:00 PM
                        </Typography>
                        <Typography variant="body2" className="text-gray-600">
                          Sat: 10:00 AM - 4:00 PM
                        </Typography>
                      </Box>
                    </Stack>
                  </Stack>
                </Box>

                {/* Map */}
                <Box className="rounded-2xl overflow-hidden shadow-lg">
                  <iframe
                    src="https://maps.google.com/maps?width=100%25&height=300&hl=en&q=40.7589,-73.9851&t=&z=14&ie=UTF8&iwloc=B&output=embed"
                    width="100%"
                    height="300"
                    allowFullScreen={true}
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    className="border-0"
                  />
                </Box>

                {/* Additional Info */}
                <GlassmorphicCard className="p-6 text-center">
                  <Typography
                    variant="h6"
                    className="mb-3 font-semibold"
                    sx={{ fontFamily: '"Playfair Display", serif' }}
                  >
                    Schedule a Consultation
                  </Typography>
                  <Typography
                    variant="body2"
                    className="text-gray-600 mb-4"
                    sx={{ fontFamily: '"Inter", sans-serif' }}
                  >
                    Book a private appointment with our stone specialists to explore 
                    our full collection and discuss your project requirements.
                  </Typography>
                  <LuxuryButton variant="outlined" size="small">
                    Book Appointment
                  </LuxuryButton>
                </GlassmorphicCard>
              </Stack>
            </Grid>
          </Grid>
        </Stack>
      </Container>
    </Box>
  );
};

export default ContactSection;