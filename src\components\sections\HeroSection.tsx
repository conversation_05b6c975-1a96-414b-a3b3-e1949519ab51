import React from 'react';
import { Container, Typography, Stack, Box } from '@mui/material';
import { ChevronDown } from 'lucide-react';
import { HeroTypography, LuxuryButton } from '../styled/StyledComponents';

const HeroSection: React.FC = () => {
  return (
    <Box
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
      sx={{
        background: `linear-gradient(135deg, rgba(51, 51, 51, 0.8) 0%, rgba(51, 51, 51, 0.4) 100%), 
                     url('https://images.unsplash.com/photo-1586609143615-019f8824129f?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwxfHxtYXJibGUlMjBzdG9uZSUyMHRleHR1cmUlMjBsdXh1cnl8ZW58MHwwfHx3aGl0ZXwxNzU0NDA3OTc5fDA&ixlib=rb-4.1.0&q=85')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed',
      }}
    >
      {/* Animated background elements */}
      <Box
        className="absolute inset-0 opacity-20"
        sx={{
          background: 'radial-gradient(circle at 20% 80%, rgba(234, 193, 71, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(195, 101, 34, 0.3) 0%, transparent 50%)',
          animation: 'float 6s ease-in-out infinite',
        }}
      />

      <Container maxWidth="lg" className="relative z-10">
        <Stack 
          direction={{ xs: 'column', lg: 'row' }} 
          spacing={8} 
          alignItems="center"
          className="min-h-screen py-20"
        >
          {/* Left Content */}
          <Box className="flex-1 text-center lg:text-left">
            <HeroTypography variant="h1">
              Luxury Stone
              <br />
              & Ceramic
              <br />
              Collection
            </HeroTypography>
            
            <Typography
              variant="h5"
              className="mb-8 text-white/90 max-w-lg"
              sx={{
                fontFamily: '"Inter", sans-serif',
                fontWeight: 300,
                lineHeight: 1.6,
                fontSize: 'clamp(1.1rem, 2.5vw, 1.5rem)',
              }}
            >
              Discover our curated selection of premium natural stones and contemporary ceramics, 
              crafted for the most discerning architectural projects.
            </Typography>

            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3} className="mb-12">
              <LuxuryButton size="large">
                Explore Collection
              </LuxuryButton>
              <LuxuryButton 
                variant="outlined" 
                size="large"
                sx={{
                  borderColor: 'rgba(255, 255, 255, 0.3)',
                  color: 'white',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(10px)',
                  '&:hover': {
                    borderColor: 'secondary.main',
                    backgroundColor: 'rgba(234, 193, 71, 0.1)',
                  }
                }}
              >
                View Catalog
              </LuxuryButton>
            </Stack>

            {/* Stats */}
            <Stack direction="row" spacing={6} className="justify-center lg:justify-start">
              <Box className="text-center">
                <Typography variant="h3" className="text-white font-bold mb-1">
                  500+
                </Typography>
                <Typography variant="body2" className="text-white/70">
                  Premium Products
                </Typography>
              </Box>
              <Box className="text-center">
                <Typography variant="h3" className="text-white font-bold mb-1">
                  25+
                </Typography>
                <Typography variant="body2" className="text-white/70">
                  Years Experience
                </Typography>
              </Box>
              <Box className="text-center">
                <Typography variant="h3" className="text-white font-bold mb-1">
                  1000+
                </Typography>
                <Typography variant="body2" className="text-white/70">
                  Satisfied Clients
                </Typography>
              </Box>
            </Stack>
          </Box>

          {/* Right Content - Floating Product Preview */}
          <Box className="flex-1 relative">
            <Box
              className="relative transform rotate-12 hover:rotate-6 transition-transform duration-700"
              sx={{
                width: { xs: 300, md: 400 },
                height: { xs: 400, md: 500 },
                margin: '0 auto',
              }}
            >
              <Box
                className="absolute inset-0 rounded-3xl overflow-hidden shadow-2xl"
                sx={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(20px)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                }}
              >
                <img
                  src="https://images.unsplash.com/photo-1707159397411-c98fe135065c?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwxfHxncmFuaXRlJTIwc3RvbmUlMjBkYXJrJTIwcG9saXNoZWR8ZW58MHwyfHxibGFja3wxNzU0NDA3OTc5fDA&ixlib=rb-4.1.0&q=85"
                  alt="Premium granite stone texture by Dmitry Kharitonov on Unsplash"
                  className="w-full h-full object-cover"
                  style={{ width: '100%', height: '100%' }}
                />
                <Box
                  className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"
                />
                <Box className="absolute bottom-6 left-6 right-6">
                  <Typography variant="h6" className="text-white font-semibold mb-1">
                    Black Galaxy Granite
                  </Typography>
                  <Typography variant="body2" className="text-white/80">
                    GRN-045
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        </Stack>

        {/* Scroll Indicator */}
        <Box className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60">
          <Stack alignItems="center" spacing={1}>
            <Typography variant="body2" className="text-xs tracking-wider uppercase">
              Scroll to explore
            </Typography>
            <ChevronDown 
              size={24} 
              className="animate-bounce"
            />
          </Stack>
        </Box>
      </Container>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(5deg); }
        }
      `}</style>
    </Box>
  );
};

export default HeroSection;