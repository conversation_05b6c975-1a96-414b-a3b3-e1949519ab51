import React, { useState } from 'react';
import { <PERSON><PERSON>, Backdrop, Container, Typography, Stack, Box, IconButton } from '@mui/material';
import { X, ChevronLeft, ChevronRight, ZoomIn } from 'lucide-react';
import { Product } from '../../types/product';
import { LuxuryButton } from '../styled/StyledComponents';

interface ProductDetailProps {
  product: Product | null;
  open: boolean;
  onClose: () => void;
}

const ProductDetail: React.FC<ProductDetailProps> = ({ product, open, onClose }) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [lightboxOpen, setLightboxOpen] = useState(false);

  if (!product) return null;

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % product.images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + product.images.length) % product.images.length);
  };

  const openLightbox = () => {
    setLightboxOpen(true);
  };

  const closeLightbox = () => {
    setLightboxOpen(false);
  };

  return (
    <>
      {/* Main Product Detail Modal */}
      <Modal
        open={open}
        onClose={onClose}
        closeAfterTransition
        BackdropComponent={Backdrop}
        BackdropProps={{
          timeout: 500,
          sx: { backgroundColor: 'rgba(0, 0, 0, 0.8)', backdropFilter: 'blur(10px)' }
        }}
      >
        <Box
          className="absolute inset-4 md:inset-8 bg-white rounded-3xl overflow-hidden 
                   shadow-2xl focus:outline-none"
          sx={{
            maxHeight: 'calc(100vh - 64px)',
            overflowY: 'auto',
          }}
        >
          {/* Close Button */}
          <IconButton
            onClick={onClose}
            className="absolute top-4 right-4 z-10 bg-white/80 backdrop-blur-sm hover:bg-white"
            sx={{ boxShadow: 2 }}
          >
            <X size={24} />
          </IconButton>

          <Stack direction={{ xs: 'column', lg: 'row' }} className="min-h-full">
            {/* Image Section */}
            <Box className="flex-1 relative bg-gray-50">
              <Box className="relative h-96 lg:h-full min-h-96">
                <img
                  src={product.images[currentImageIndex]}
                  alt={`${product.name} - ${product.description.split('.')[0]} by photographer on Unsplash`}
                  className="w-full h-full object-cover cursor-zoom-in"
                  onClick={openLightbox}
                  style={{ width: '100%', height: '100%' }}
                />

                {/* Image Navigation */}
                {product.images.length > 1 && (
                  <>
                    <IconButton
                      onClick={prevImage}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 
                               bg-white/80 backdrop-blur-sm hover:bg-white"
                      sx={{ boxShadow: 2 }}
                    >
                      <ChevronLeft size={24} />
                    </IconButton>
                    <IconButton
                      onClick={nextImage}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 
                               bg-white/80 backdrop-blur-sm hover:bg-white"
                      sx={{ boxShadow: 2 }}
                    >
                      <ChevronRight size={24} />
                    </IconButton>
                  </>
                )}

                {/* Zoom Indicator */}
                <Box
                  className="absolute bottom-4 right-4 bg-white/80 backdrop-blur-sm 
                           rounded-full p-2 cursor-pointer hover:bg-white transition-colors"
                  onClick={openLightbox}
                  sx={{ boxShadow: 2 }}
                >
                  <ZoomIn size={20} />
                </Box>

                {/* Image Indicators */}
                {product.images.length > 1 && (
                  <Stack
                    direction="row"
                    spacing={1}
                    className="absolute bottom-4 left-1/2 transform -translate-x-1/2"
                  >
                    {product.images.map((_, index) => (
                      <Box
                        key={index}
                        className={`w-2 h-2 rounded-full cursor-pointer transition-colors ${
                          index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                        }`}
                        onClick={() => setCurrentImageIndex(index)}
                      />
                    ))}
                  </Stack>
                )}
              </Box>

              {/* Thumbnail Strip */}
              {product.images.length > 1 && (
                <Stack direction="row" spacing={2} className="p-4 overflow-x-auto">
                  {product.images.map((image, index) => (
                    <Box
                      key={index}
                      className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden cursor-pointer 
                               border-2 transition-all ${
                                 index === currentImageIndex 
                                   ? 'border-yellow-400 shadow-lg' 
                                   : 'border-gray-200 hover:border-gray-300'
                               }`}
                      onClick={() => setCurrentImageIndex(index)}
                    >
                      <img
                        src={image}
                        alt={`${product.name} thumbnail ${index + 1}`}
                        className="w-full h-full object-cover"
                        style={{ width: '100%', height: '100%' }}
                      />
                    </Box>
                  ))}
                </Stack>
              )}
            </Box>

            {/* Content Section */}
            <Box className="flex-1 p-8 lg:p-12">
              <Stack spacing={6}>
                {/* Header */}
                <Box>
                  <Stack direction="row" justifyContent="space-between" alignItems="start" className="mb-4">
                    <Typography
                      variant="h3"
                      sx={{
                        fontFamily: '"Playfair Display", serif',
                        fontWeight: 600,
                        color: 'primary.main'
                      }}
                    >
                      {product.name}
                    </Typography>
                    {product.featured && (
                      <Box
                        className="px-3 py-1 rounded-full"
                        sx={{
                          background: 'linear-gradient(135deg, rgba(234, 193, 71, 0.2) 0%, rgba(195, 101, 34, 0.2) 100%)',
                          border: '1px solid rgba(234, 193, 71, 0.3)',
                        }}
                      >
                        <Typography variant="caption" className="text-yellow-700 font-semibold">
                          Featured Product
                        </Typography>
                      </Box>
                    )}
                  </Stack>

                  <Typography
                    variant="h6"
                    className="text-gray-500 font-mono mb-2"
                    sx={{ fontFamily: '"Inter", monospace' }}
                  >
                    Product Code: {product.code}
                  </Typography>

                  <Typography
                    variant="body2"
                    className="text-gray-500 uppercase tracking-wider"
                  >
                    {product.category} Collection
                  </Typography>
                </Box>

                {/* Description */}
                <Box>
                  <Typography
                    variant="h6"
                    className="mb-3 font-semibold"
                    sx={{ fontFamily: '"Inter", sans-serif' }}
                  >
                    Description
                  </Typography>
                  <Typography
                    variant="body1"
                    className="text-gray-700 leading-relaxed"
                    sx={{ fontFamily: '"Inter", sans-serif' }}
                  >
                    {product.description}
                  </Typography>
                </Box>

                {/* Specifications */}
                <Box>
                  <Typography
                    variant="h6"
                    className="mb-3 font-semibold"
                    sx={{ fontFamily: '"Inter", sans-serif' }}
                  >
                    Specifications
                  </Typography>
                  <Stack spacing={2}>
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="body2" className="text-gray-600">
                        Material Type:
                      </Typography>
                      <Typography variant="body2" className="font-medium capitalize">
                        {product.category}
                      </Typography>
                    </Stack>
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="body2" className="text-gray-600">
                        Product Code:
                      </Typography>
                      <Typography variant="body2" className="font-medium font-mono">
                        {product.code}
                      </Typography>
                    </Stack>
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="body2" className="text-gray-600">
                        Available Images:
                      </Typography>
                      <Typography variant="body2" className="font-medium">
                        {product.images.length} High-Resolution
                      </Typography>
                    </Stack>
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="body2" className="text-gray-600">
                        Added Date:
                      </Typography>
                      <Typography variant="body2" className="font-medium">
                        {new Date(product.createdAt).toLocaleDateString()}
                      </Typography>
                    </Stack>
                  </Stack>
                </Box>

                {/* Actions */}
                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3}>
                  <LuxuryButton fullWidth>
                    Request Quote
                  </LuxuryButton>
                  <LuxuryButton 
                    variant="outlined" 
                    fullWidth
                    sx={{
                      borderColor: 'primary.main',
                      color: 'primary.main',
                      '&:hover': {
                        borderColor: 'secondary.main',
                        backgroundColor: 'secondary.main',
                        color: 'secondary.contrastText',
                      }
                    }}
                  >
                    Download Catalog
                  </LuxuryButton>
                </Stack>
              </Stack>
            </Box>
          </Stack>
        </Box>
      </Modal>

      {/* Lightbox Modal */}
      <Modal
        open={lightboxOpen}
        onClose={closeLightbox}
        closeAfterTransition
        BackdropComponent={Backdrop}
        BackdropProps={{
          timeout: 500,
          sx: { backgroundColor: 'rgba(0, 0, 0, 0.95)' }
        }}
      >
        <Box
          className="absolute inset-0 flex items-center justify-center p-4 focus:outline-none"
          onClick={closeLightbox}
        >
          <IconButton
            onClick={closeLightbox}
            className="absolute top-4 right-4 z-10 text-white hover:bg-white/10"
          >
            <X size={32} />
          </IconButton>

          <img
            src={product.images[currentImageIndex]}
            alt={`${product.name} full size - ${product.description.split('.')[0]} by photographer on Unsplash`}
            className="max-w-full max-h-full object-contain"
            onClick={(e) => e.stopPropagation()}
            style={{ maxWidth: '100%', maxHeight: '100%' }}
          />

          {product.images.length > 1 && (
            <>
              <IconButton
                onClick={(e) => { e.stopPropagation(); prevImage(); }}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:bg-white/10"
              >
                <ChevronLeft size={32} />
              </IconButton>
              <IconButton
                onClick={(e) => { e.stopPropagation(); nextImage(); }}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:bg-white/10"
              >
                <ChevronRight size={32} />
              </IconButton>
            </>
          )}
        </Box>
      </Modal>
    </>
  );
};

export default ProductDetail;