import React, { useState, useMemo } from 'react';
import { Container, Typography, Stack, Box, TextField, InputAdornment } from '@mui/material';
import { Search } from 'lucide-react';
import { ProductCard, FilterChip } from '../styled/StyledComponents';
import { Product, ProductCategory, ProductFilter } from '../../types/product';
import { mockProducts } from '../../data/mockData';

interface ProductGalleryProps {
  selectedCategory?: ProductCategory;
  onProductSelect: (product: Product) => void;
}

const categories: { value: ProductCategory; label: string }[] = [
  { value: 'marble', label: 'Marble' },
  { value: 'granite', label: 'Granite' },
  { value: 'travertine', label: 'Travertine' },
  { value: 'ceramic', label: 'Ceramic' }
];

const ProductGallery: React.FC<ProductGalleryProps> = ({ selectedCategory, onProductSelect }) => {
  const [filter, setFilter] = useState<ProductFilter>({
    category: selectedCategory,
    search: '',
    featured: undefined
  });

  const filteredProducts = useMemo(() => {
    return mockProducts.filter(product => {
      if (filter.category && product.category !== filter.category) return false;
      if (filter.search && !product.name.toLowerCase().includes(filter.search.toLowerCase()) &&
          !product.code.toLowerCase().includes(filter.search.toLowerCase())) return false;
      if (filter.featured !== undefined && product.featured !== filter.featured) return false;
      return true;
    });
  }, [filter]);

  const handleCategoryFilter = (category: ProductCategory | undefined) => {
    setFilter(prev => ({ ...prev, category }));
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilter(prev => ({ ...prev, search: event.target.value }));
  };

  return (
    <Box className="py-20 bg-white">
      <Container maxWidth="lg">
        <Stack spacing={8}>
          {/* Section Header */}
          <Box className="text-center">
            <Typography
              variant="h2"
              className="mb-4"
              sx={{
                fontFamily: '"Playfair Display", serif',
                fontWeight: 600,
                color: 'primary.main'
              }}
            >
              Product Gallery
            </Typography>
            <Typography
              variant="h6"
              className="text-gray-600 max-w-2xl mx-auto"
              sx={{ fontFamily: '"Inter", sans-serif', fontWeight: 300 }}
            >
              Browse our extensive collection of premium stones and ceramics, 
              each piece carefully selected for exceptional quality and beauty.
            </Typography>
          </Box>

          {/* Filters */}
          <Stack spacing={4}>
            {/* Search */}
            <Box className="max-w-md mx-auto w-full">
              <TextField
                fullWidth
                placeholder="Search by name or product code..."
                value={filter.search}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search size={20} className="text-gray-400" />
                    </InputAdornment>
                  ),
                  sx: {
                    borderRadius: '50px',
                    backgroundColor: 'rgba(0, 0, 0, 0.02)',
                    '& .MuiOutlinedInput-notchedOutline': {
                      border: 'none'
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      border: 'none'
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      border: '2px solid',
                      borderColor: 'secondary.main'
                    }
                  }
                }}
              />
            </Box>

            {/* Category Filters */}
            <Stack direction="row" spacing={2} className="justify-center flex-wrap gap-2">
              <FilterChip
                onClick={() => handleCategoryFilter(undefined)}
                className={!filter.category ? 'active' : ''}
              >
                All Categories
              </FilterChip>
              {categories.map(category => (
                <FilterChip
                  key={category.value}
                  onClick={() => handleCategoryFilter(category.value)}
                  className={filter.category === category.value ? 'active' : ''}
                >
                  {category.label}
                </FilterChip>
              ))}
              <FilterChip
                onClick={() => setFilter(prev => ({ 
                  ...prev, 
                  featured: prev.featured === true ? undefined : true 
                }))}
                className={filter.featured === true ? 'active' : ''}
              >
                Featured
              </FilterChip>
            </Stack>
          </Stack>

          {/* Results Count */}
          <Box className="text-center">
            <Typography variant="body2" className="text-gray-500">
              Showing {filteredProducts.length} of {mockProducts.length} products
            </Typography>
          </Box>

          {/* Products Grid */}
          <Box className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProducts.map((product, index) => (
              <ProductCard
                key={product.id}
                onClick={() => onProductSelect(product)}
                className="group"
                sx={{
                  animation: `fadeInUp 0.6s ease forwards ${index * 0.1}s`,
                  opacity: 0,
                  transform: 'translateY(30px)',
                }}
              >
                <Box className="relative overflow-hidden">
                  <img
                    src={product.images[0]}
                    alt={`${product.name} - ${product.description.split('.')[0]} by photographer on Unsplash`}
                    className="product-image w-full h-64 object-cover"
                    style={{ width: '100%', height: '256px' }}
                  />
                  
                  {/* Overlay on hover */}
                  <Box
                    className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 
                             transition-opacity duration-300 flex items-center justify-center"
                  >
                    <Typography
                      variant="button"
                      className="text-white font-semibold px-6 py-2 rounded-full border-2 border-white/50 
                               backdrop-blur-sm bg-white/10 hover:bg-white/20 transition-colors"
                    >
                      View Details
                    </Typography>
                  </Box>

                  {/* Featured Badge */}
                  {product.featured && (
                    <Box
                      className="absolute top-4 left-4 px-3 py-1 rounded-full"
                      sx={{
                        background: 'linear-gradient(135deg, rgba(234, 193, 71, 0.9) 0%, rgba(195, 101, 34, 0.9) 100%)',
                        backdropFilter: 'blur(10px)',
                      }}
                    >
                      <Typography variant="caption" className="text-white font-semibold">
                        Featured
                      </Typography>
                    </Box>
                  )}
                </Box>

                <Box className="p-6">
                  <Stack direction="row" justifyContent="space-between" alignItems="start" className="mb-2">
                    <Typography
                      variant="h6"
                      className="font-semibold group-hover:text-yellow-600 transition-colors"
                      sx={{ fontFamily: '"Playfair Display", serif' }}
                    >
                      {product.name}
                    </Typography>
                    <Typography
                      variant="body2"
                      className="text-gray-500 font-mono bg-gray-100 px-2 py-1 rounded"
                    >
                      {product.code}
                    </Typography>
                  </Stack>

                  <Typography
                    variant="body2"
                    className="text-gray-600 mb-4 line-clamp-2"
                    sx={{ fontFamily: '"Inter", sans-serif' }}
                  >
                    {product.description}
                  </Typography>

                  <Box className="flex items-center justify-between">
                    <Typography
                      variant="caption"
                      className="text-gray-500 uppercase tracking-wider"
                    >
                      {product.category}
                    </Typography>
                    <Box className="w-8 h-8 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 
                                  group-hover:scale-110 transition-transform duration-300" />
                  </Box>
                </Box>
              </ProductCard>
            ))}
          </Box>

          {/* Empty State */}
          {filteredProducts.length === 0 && (
            <Box className="text-center py-16">
              <Typography variant="h6" className="text-gray-500 mb-2">
                No products found
              </Typography>
              <Typography variant="body2" className="text-gray-400">
                Try adjusting your filters or search terms
              </Typography>
            </Box>
          )}
        </Stack>
      </Container>

      <style jsx>{`
        @keyframes fadeInUp {
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
    </Box>
  );
};

export default ProductGallery;