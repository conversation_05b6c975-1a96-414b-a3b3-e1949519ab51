import { Product } from '../types/product';

export const mockProducts: Product[] = [
  {
    id: '1',
    code: 'MBL-012',
    name: 'Carrara White Marble',
    description: 'Premium Italian Carrara marble with distinctive veining patterns. Perfect for luxury countertops and flooring applications.',
    category: 'marble' as const,
    images: [
      'https://images.unsplash.com/photo-1586609143615-019f8824129f?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwxfHxtYXJibGUlMjBzdG9uZSUyMHRleHR1cmUlMjBsdXh1cnl8ZW58MHwwfHx3aGl0ZXwxNzU0NDA3OTc5fDA&ixlib=rb-4.1.0&q=85',
      'https://images.unsplash.com/photo-1558346648-9757f2fa4474?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwyfHxtYXJibGUlMjBzdG9uZSUyMHRleHR1cmUlMjBsdXh1cnl8ZW58MHwwfHx3aGl0ZXwxNzU0NDA3OTc5fDA&ixlib=rb-4.1.0&q=85'
    ],
    featured: true,
    createdAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    code: 'GRN-045',
    name: 'Black Galaxy Granite',
    description: 'Stunning black granite with golden speckles that shimmer like stars. Ideal for premium kitchen countertops.',
    category: 'granite' as const,
    images: [
      'https://images.unsplash.com/photo-1707159397411-c98fe135065c?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwxfHxncmFuaXRlJTIwc3RvbmUlMjBkYXJrJTIwcG9saXNoZWR8ZW58MHwyfHxibGFja3wxNzU0NDA3OTc5fDA&ixlib=rb-4.1.0&q=85',
      'https://images.unsplash.com/photo-1708367265975-d1392470b3c1?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw5fHxncmFuaXRlJTIwc3RvbmUlMjBkYXJrJTIwcG9saXNoZWR8ZW58MHwyfHxibGFja3wxNzU0NDA3OTc5fDA&ixlib=rb-4.1.0&q=85'
    ],
    featured: true,
    createdAt: '2024-01-20T14:30:00Z'
  },
  {
    id: '3',
    code: 'TRV-028',
    name: 'Roman Travertine',
    description: 'Classic Roman travertine with warm beige tones and natural texture. Perfect for both interior and exterior applications.',
    category: 'travertine' as const,
    images: [
      'https://images.unsplash.com/photo-1640007689958-d49cef861e4d?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwxfHx0cmF2ZXJ0aW5lJTIwbGltZXN0b25lJTIwYmVpZ2UlMjBuYXR1cmFsfGVufDB8Mnx8b3JhbmdlfDE3NTQ0MDc5Nzl8MA&ixlib=rb-4.1.0&q=85'
    ],
    featured: false,
    createdAt: '2024-01-25T09:15:00Z'
  },
  {
    id: '4',
    code: 'CER-156',
    name: 'Modern Geometric Tiles',
    description: 'Contemporary ceramic tiles with geometric patterns. Available in multiple color combinations for modern spaces.',
    category: 'ceramic' as const,
    images: [
      'https://images.unsplash.com/photo-1712488093707-b30afffaea77?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwxfHxjZXJhbWljJTIwdGlsZXMlMjBtb2Rlcm4lMjBjb250ZW1wb3Jhcnl8ZW58MHwyfHx8MTc1NDQwNzk3OXww&ixlib=rb-4.1.0&q=85',
      'https://images.unsplash.com/photo-1646489553899-c4c4b5a72ccc?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw3fHxjZXJhbWljJTIwdGlsZXMlMjBtb2Rlcm4lMjBjb250ZW1wb3Jhcnl8ZW58MHwyfHx8MTc1NDQwNzk3OXww&ixlib=rb-4.1.0&q=85'
    ],
    featured: true,
    createdAt: '2024-02-01T16:45:00Z'
  },
  {
    id: '5',
    code: 'MBL-089',
    name: 'Calacatta Gold Marble',
    description: 'Luxurious Calacatta marble with dramatic gold veining. The epitome of elegance for high-end residential projects.',
    category: 'marble' as const,
    images: [
      'https://images.unsplash.com/photo-1584077779924-1ee14a8bf6dd?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw1fHxtYXJibGUlMjBzdG9uZSUyMHRleHR1cmUlMjBsdXh1cnl8ZW58MHwwfHx3aGl0ZXwxNzU0NDA3OTc5fDA&ixlib=rb-4.1.0&q=85'
    ],
    featured: false,
    createdAt: '2024-02-05T11:20:00Z'
  },
  {
    id: '6',
    code: 'GRN-078',
    name: 'Emerald Pearl Granite',
    description: 'Sophisticated granite with emerald green undertones and pearl-like crystalline structure.',
    category: 'granite' as const,
    images: [
      'https://images.unsplash.com/photo-1708426894988-f55473a323e8?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw1fHxncmFuaXRlJTIwc3RvbmUlMjBkYXJrJTIwcG9saXNoZWR8ZW58MHwyfHxibGFja3wxNzU0NDA3OTc5fDA&ixlib=rb-4.1.0&q=85'
    ],
    featured: false,
    createdAt: '2024-02-10T13:00:00Z'
  }
];

export const categoryImages = {
  marble: 'https://images.unsplash.com/photo-1586609143615-019f8824129f?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwxfHxtYXJibGUlMjBzdG9uZSUyMHRleHR1cmUlMjBsdXh1cnl8ZW58MHwwfHx3aGl0ZXwxNzU0NDA3OTc5fDA&ixlib=rb-4.1.0&q=85',
  granite: 'https://images.unsplash.com/photo-1707159397411-c98fe135065c?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwxfHxncmFuaXRlJTIwc3RvbmUlMjBkYXJrJTIwcG9saXNoZWR8ZW58MHwyfHxibGFja3wxNzU0NDA3OTc5fDA&ixlib=rb-4.1.0&q=85',
  travertine: 'https://images.unsplash.com/photo-1640007689958-d49cef861e4d?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwxfHx0cmF2ZXJ0aW5lJTIwbGltZXN0b25lJTIwYmVpZ2UlMjBuYXR1cmFsfGVufDB8Mnx8b3JhbmdlfDE3NTQ0MDc5Nzl8MA&ixlib=rb-4.1.0&q=85',
  ceramic: 'https://images.unsplash.com/photo-1712488093707-b30afffaea77?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwxfHxjZXJhbWljJTIwdGlsZXMlMjBtb2Rlcm4lMjBjb250ZW1wb3Jhcnl8ZW58MHwyfHx8MTc1NDQwNzk3OXww&ixlib=rb-4.1.0&q=85'
};