import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    primary: {
      main: '#333333',
      light: '#666666',
      dark: '#1a1a1a',
      contrastText: '#ffffff'
    },
    secondary: {
      main: '#eac147',
      light: '#f0d470',
      dark: '#c19a2e',
      contrastText: '#333333'
    },
    background: {
      default: '#f2f2f2',
      paper: '#ffffff'
    },
    text: {
      primary: '#333333',
      secondary: '#666666',
      disabled: '#999999'
    },
    grey: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#eeeeee',
      300: '#e0e0e0',
      400: '#bdbdbd',
      500: '#9e9e9e',
      600: '#757575',
      700: '#616161',
      800: '#424242',
      900: '#212121'
    },
    common: {
      black: '#000000',
      white: '#ffffff'
    },
    divider: '#ded8d3'
  },
  typography: {
    fontFamily: '"Playfair Display", "Inter", serif, sans-serif',
    h1: {
      fontFamily: '"Playfair Display", serif',
      fontWeight: 700,
      fontSize: '3.5rem',
      lineHeight: 1.2,
      letterSpacing: '-0.02em'
    },
    h2: {
      fontFamily: '"Playfair Display", serif',
      fontWeight: 600,
      fontSize: '2.75rem',
      lineHeight: 1.3,
      letterSpacing: '-0.01em'
    },
    h3: {
      fontFamily: '"Playfair Display", serif',
      fontWeight: 600,
      fontSize: '2.25rem',
      lineHeight: 1.3
    },
    h4: {
      fontFamily: '"Playfair Display", serif',
      fontWeight: 500,
      fontSize: '1.75rem',
      lineHeight: 1.4
    },
    h5: {
      fontFamily: '"Inter", sans-serif',
      fontWeight: 600,
      fontSize: '1.25rem',
      lineHeight: 1.4
    },
    h6: {
      fontFamily: '"Inter", sans-serif',
      fontWeight: 600,
      fontSize: '1.125rem',
      lineHeight: 1.4
    },
    body1: {
      fontFamily: '"Inter", sans-serif',
      fontWeight: 400,
      fontSize: '1rem',
      lineHeight: 1.6
    },
    body2: {
      fontFamily: '"Inter", sans-serif',
      fontWeight: 400,
      fontSize: '0.875rem',
      lineHeight: 1.5
    },
    button: {
      fontFamily: '"Inter", sans-serif',
      fontWeight: 500,
      fontSize: '0.875rem',
      textTransform: 'none',
      letterSpacing: '0.02em'
    }
  },
  shape: {
    borderRadius: 12
  },
  shadows: [
    'none',
    '0px 2px 4px rgba(51, 51, 51, 0.08)',
    '0px 4px 8px rgba(51, 51, 51, 0.12)',
    '0px 8px 16px rgba(51, 51, 51, 0.16)',
    '0px 12px 24px rgba(51, 51, 51, 0.20)',
    '0px 16px 32px rgba(51, 51, 51, 0.24)',
    '0px 20px 40px rgba(51, 51, 51, 0.28)',
    '0px 24px 48px rgba(51, 51, 51, 0.32)',
    '0px 28px 56px rgba(51, 51, 51, 0.36)',
    '0px 32px 64px rgba(51, 51, 51, 0.40)',
    '0px 36px 72px rgba(51, 51, 51, 0.44)',
    '0px 40px 80px rgba(51, 51, 51, 0.48)',
    '0px 44px 88px rgba(51, 51, 51, 0.52)',
    '0px 48px 96px rgba(51, 51, 51, 0.56)',
    '0px 52px 104px rgba(51, 51, 51, 0.60)',
    '0px 56px 112px rgba(51, 51, 51, 0.64)',
    '0px 60px 120px rgba(51, 51, 51, 0.68)',
    '0px 64px 128px rgba(51, 51, 51, 0.72)',
    '0px 68px 136px rgba(51, 51, 51, 0.76)',
    '0px 72px 144px rgba(51, 51, 51, 0.80)',
    '0px 76px 152px rgba(51, 51, 51, 0.84)',
    '0px 80px 160px rgba(51, 51, 51, 0.88)',
    '0px 84px 168px rgba(51, 51, 51, 0.92)',
    '0px 88px 176px rgba(51, 51, 51, 0.96)',
    '0px 92px 184px rgba(51, 51, 51, 1.00)'
  ]
});

export default theme;