export interface Product {
  id: string;
  code: string;
  name: string;
  description: string;
  category: ProductCategory;
  images: string[];
  featured: boolean;
  createdAt: string;
}

export type ProductCategory = 'marble' | 'granite' | 'travertine' | 'ceramic';

export interface ProductFilter {
  category?: ProductCategory;
  search?: string;
  featured?: boolean;
}

export interface Language {
  code: 'en' | 'ar';
  name: string;
  direction: 'ltr' | 'rtl';
}